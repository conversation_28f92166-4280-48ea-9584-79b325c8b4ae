// src/tests/unit/components/UnifiedMediaUpload.test.tsx
import { render, screen, fireEvent, waitFor } from '../../setup/test-utils';
import userEvent from '@testing-library/user-event';
import UnifiedMediaUpload from '@/components/upload/UnifiedMediaUpload';
import { MediaItem, GpxFile } from '@/types/trip';

// Mock della funzione fetch globale
global.fetch = jest.fn();

// Mock di URL.createObjectURL
URL.createObjectURL = jest.fn(() => 'mock-url');
URL.revokeObjectURL = jest.fn();

describe('UnifiedMediaUpload', () => {
  // Mock delle funzioni di callback
  const mockOnAddMedia = jest.fn();
  const mockOnRemoveMedia = jest.fn();
  const mockOnUpdateCaption = jest.fn();
  const mockOnGpxUpload = jest.fn();
  const mockOnGpxRemove = jest.fn();
  
  // <PERSON>ti di esempio
  const mockMediaItems: MediaItem[] = [
    {
      id: '1',
      type: 'image',
      url: 'https://example.com/image1.jpg',
      caption: 'Immagine test'
    },
    {
      id: '2',
      type: 'video',
      url: 'https://www.youtube.com/embed/abcdef12345',
      thumbnailUrl: 'https://img.youtube.com/vi/abcdef12345/maxresdefault.jpg',
      caption: 'Video test'
    }
  ];

  const mockGpxFile: GpxFile = {
    url: 'https://example.com/test.gpx',
    filename: 'test-track.gpx',
    waypoints: 100,
    distance: 25.5,
    elevationGain: 500,
    isValid: true
  };

  const defaultProps = {
    mediaItems: [],
    onAddMedia: mockOnAddMedia,
    onRemoveMedia: mockOnRemoveMedia,
    onUpdateCaption: mockOnUpdateCaption,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock della risposta per l'upload delle immagini
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({ url: 'https://example.com/uploaded-image.jpg' })
    });
  });

  describe('Basic Rendering', () => {
    it('deve renderizzare correttamente con configurazione default', () => {
      render(<UnifiedMediaUpload {...defaultProps} />);
      
      expect(screen.getByText('Multimedia')).toBeInTheDocument();
      expect(screen.getByText('Carica Immagini')).toBeInTheDocument();
      expect(screen.getByText('Aggiungi Video YouTube')).toBeInTheDocument();
      expect(screen.queryByText('Carica File GPX')).not.toBeInTheDocument();
    });

    it('deve renderizzare la sezione GPX quando abilitata', () => {
      render(
        <UnifiedMediaUpload 
          {...defaultProps}
          config={{ enableGpx: true }}
          onGpxUpload={mockOnGpxUpload}
        />
      );
      
      expect(screen.getByText('Carica File GPX')).toBeInTheDocument();
    });

    it('deve nascondere YouTube quando disabilitato', () => {
      render(
        <UnifiedMediaUpload 
          {...defaultProps}
          config={{ enableYoutube: false }}
        />
      );
      
      expect(screen.queryByText('Aggiungi Video YouTube')).not.toBeInTheDocument();
    });
  });

  describe('Media Items Display', () => {
    it('deve renderizzare correttamente la lista dei media esistenti', () => {
      render(
        <UnifiedMediaUpload 
          {...defaultProps}
          mediaItems={mockMediaItems}
        />
      );
      
      expect(screen.getByText('Media Aggiunti (2)')).toBeInTheDocument();
      
      // Verifica che le immagini siano visualizzate
      const images = screen.getAllByRole('img');
      expect(images.length).toBeGreaterThanOrEqual(2);
      
      // Verifica che i campi di caption siano presenti
      const captionInputs = screen.getAllByPlaceholderText('Aggiungi una didascalia...');
      expect(captionInputs).toHaveLength(2);
      
      // Verifica che i valori delle caption siano corretti
      expect(captionInputs[0]).toHaveValue('Immagine test');
      expect(captionInputs[1]).toHaveValue('Video test');
    });

    it('deve permettere l\'eliminazione di un media', async () => {
      render(
        <UnifiedMediaUpload 
          {...defaultProps}
          mediaItems={mockMediaItems}
        />
      );
      
      // Trova e clicca sul pulsante di eliminazione del primo media (pulsante X rosso)
      const deleteButtons = screen.getAllByRole('button');
      const removeButtons = deleteButtons.filter(btn => 
        btn.className.includes('bg-red-500') && btn.className.includes('rounded-full')
      );
      fireEvent.click(removeButtons[0]);

      // Verifica che la callback sia stata chiamata con l'ID corretto
      expect(mockOnRemoveMedia).toHaveBeenCalledWith('1');
    });

    it('deve permettere la modifica della didascalia', async () => {
      render(
        <UnifiedMediaUpload 
          {...defaultProps}
          mediaItems={mockMediaItems}
        />
      );
      
      const captionInput = screen.getAllByPlaceholderText('Aggiungi una didascalia...')[0];
      
      // Modifica la didascalia
      await userEvent.clear(captionInput);
      await userEvent.type(captionInput, 'Nuova');

      // Verifica che la callback sia stata chiamata con l'ultima modifica
      await waitFor(() => {
        expect(mockOnUpdateCaption).toHaveBeenLastCalledWith('1', 'Nuova');
      });
    });
  });

  describe('Image Upload', () => {
    it('deve gestire l\'upload di immagini tramite file input', async () => {
      render(<UnifiedMediaUpload {...defaultProps} />);
      
      const fileInput = document.getElementById('unified-images') as HTMLInputElement;
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      await userEvent.upload(fileInput, file);

      await waitFor(() => {
        expect(mockOnAddMedia).toHaveBeenCalledWith({
          type: 'image',
          url: 'https://example.com/uploaded-image.jpg',
          caption: '',
          thumbnailUrl: undefined
        });
      });
    });

    it('deve gestire errori di upload', async () => {
      // Mock di una risposta di errore
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        json: async () => ({ error: 'Upload failed' })
      });

      render(<UnifiedMediaUpload {...defaultProps} />);
      
      const fileInput = document.getElementById('unified-images') as HTMLInputElement;
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      await userEvent.upload(fileInput, file);

      await waitFor(() => {
        expect(screen.getByText(/Upload failed/)).toBeInTheDocument();
      });

      expect(mockOnAddMedia).not.toHaveBeenCalled();
    });

    it.skip('deve validare il tipo di file', async () => {
      render(<UnifiedMediaUpload {...defaultProps} />);
      
      const fileInput = document.getElementById('unified-images') as HTMLInputElement;
      const file = new File(['test'], 'test.txt', { type: 'text/plain' });

      await userEvent.upload(fileInput, file);

      await waitFor(() => {
        expect(screen.getByText(/non è un'immagine valida/)).toBeInTheDocument();
      });

      expect(mockOnAddMedia).not.toHaveBeenCalled();
    });

    it('deve rispettare il limite di dimensione configurabile', async () => {
      render(
        <UnifiedMediaUpload 
          {...defaultProps} 
          config={{ maxImageSize: 1 }} // 1MB limit
        />
      );
      
      const fileInput = document.getElementById('unified-images') as HTMLInputElement;
      // File troppo grande (2MB)
      const largeFile = new File(['x'.repeat(2 * 1024 * 1024)], 'large.jpg', { type: 'image/jpeg' });

      await userEvent.upload(fileInput, largeFile);

      await waitFor(() => {
        expect(screen.getByText(/troppo grande \(max 1MB\)/)).toBeInTheDocument();
      });

      expect(mockOnAddMedia).not.toHaveBeenCalled();
    });
  });

  describe('YouTube Integration', () => {
    it('deve permettere l\'aggiunta di video YouTube validi', async () => {
      render(<UnifiedMediaUpload {...defaultProps} />);
      
      const youtubeInput = screen.getByPlaceholderText('https://www.youtube.com/watch?v=...');
      const addButton = screen.getByRole('button', { name: 'Aggiungi' });

      await userEvent.type(youtubeInput, 'https://www.youtube.com/watch?v=dQw4w9WgXcQ');
      fireEvent.click(addButton);

      await waitFor(() => {
        expect(mockOnAddMedia).toHaveBeenCalledWith({
          type: 'video',
          url: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
          caption: '',
          thumbnailUrl: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
        });
      });

      // Verifica che l'input sia stato pulito
      expect(youtubeInput).toHaveValue('');
    });

    it('deve validare l\'URL YouTube', async () => {
      render(<UnifiedMediaUpload {...defaultProps} />);
      
      const youtubeInput = screen.getByPlaceholderText('https://www.youtube.com/watch?v=...');
      const addButton = screen.getByRole('button', { name: 'Aggiungi' });

      await userEvent.type(youtubeInput, 'https://invalid-url.com');
      fireEvent.click(addButton);

      await waitFor(() => {
        expect(screen.getByText(/URL YouTube non valido/)).toBeInTheDocument();
      });

      expect(mockOnAddMedia).not.toHaveBeenCalled();
    });

    it('non deve mostrare la sezione YouTube quando disabilitata', () => {
      render(
        <UnifiedMediaUpload 
          {...defaultProps}
          config={{ enableYoutube: false }}
        />
      );

      expect(screen.queryByText('Aggiungi Video YouTube')).not.toBeInTheDocument();
    });
  });

  describe('GPX Upload', () => {
    it('deve mostrare la sezione GPX quando abilitata', () => {
      render(
        <UnifiedMediaUpload 
          {...defaultProps}
          config={{ enableGpx: true }}
          onGpxUpload={mockOnGpxUpload}
        />
      );

      expect(screen.getByText('Carica File GPX')).toBeInTheDocument();
      expect(screen.getByText('Seleziona file GPX')).toBeInTheDocument();
    });

    it('deve mostrare il file GPX corrente quando presente', () => {
      render(
        <UnifiedMediaUpload 
          {...defaultProps}
          gpxFile={mockGpxFile}
          config={{ enableGpx: true }}
          onGpxUpload={mockOnGpxUpload}
          onGpxRemove={mockOnGpxRemove}
        />
      );

      expect(screen.getByText('test-track.gpx')).toBeInTheDocument();
      expect(screen.getByText('(25.5 km, 100 waypoints)')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Rimuovi' })).toBeInTheDocument();
    });

    it('deve permettere la rimozione del file GPX', () => {
      render(
        <UnifiedMediaUpload 
          {...defaultProps}
          gpxFile={mockGpxFile}
          config={{ enableGpx: true }}
          onGpxUpload={mockOnGpxUpload}
          onGpxRemove={mockOnGpxRemove}
        />
      );

      const removeButton = screen.getByRole('button', { name: 'Rimuovi' });
      fireEvent.click(removeButton);

      expect(mockOnGpxRemove).toHaveBeenCalled();
    });
  });

  describe('Drag and Drop', () => {
    it('deve gestire il drag over', () => {
      render(<UnifiedMediaUpload {...defaultProps} />);
      
      const dropZone = screen.getByText(/Clicca per selezionare o trascina le immagini qui/);
      
      fireEvent.dragOver(dropZone);
      
      expect(screen.getByText('Rilascia le immagini qui')).toBeInTheDocument();
    });

    it('deve gestire il drop di file', async () => {
      render(<UnifiedMediaUpload {...defaultProps} />);
      
      const dropZone = screen.getByText(/Clicca per selezionare o trascina le immagini qui/);
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      fireEvent.drop(dropZone, {
        dataTransfer: {
          files: [file],
        },
      });

      await waitFor(() => {
        expect(mockOnAddMedia).toHaveBeenCalledWith({
          type: 'image',
          url: 'https://example.com/uploaded-image.jpg',
          caption: '',
          thumbnailUrl: undefined
        });
      });
    });
  });

  describe('Configuration', () => {
    it('deve rispettare tutte le configurazioni', () => {
      render(
        <UnifiedMediaUpload 
          {...defaultProps}
          config={{
            enableYoutube: false,
            enableGpx: true,
            maxImageSize: 5,
          }}
          onGpxUpload={mockOnGpxUpload}
        />
      );

      expect(screen.queryByText('Aggiungi Video YouTube')).not.toBeInTheDocument();
      expect(screen.getByText('Carica File GPX')).toBeInTheDocument();
      expect(screen.getByText(/PNG, JPG, WebP fino a 5MB/)).toBeInTheDocument();
    });

    it('deve funzionare con configurazione vuota (defaults)', () => {
      render(<UnifiedMediaUpload {...defaultProps} config={{}} />);
      
      expect(screen.getByText('Aggiungi Video YouTube')).toBeInTheDocument();
      expect(screen.queryByText('Carica File GPX')).not.toBeInTheDocument();
      expect(screen.getByText(/PNG, JPG, WebP fino a 10MB/)).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('deve mostrare errori di upload', async () => {
      render(<UnifiedMediaUpload {...defaultProps} />);
      
      const youtubeInput = screen.getByPlaceholderText('https://www.youtube.com/watch?v=...');
      const addButton = screen.getByRole('button', { name: 'Aggiungi' });

      await userEvent.type(youtubeInput, 'invalid-url');
      fireEvent.click(addButton);

      await waitFor(() => {
        expect(screen.getByText(/URL YouTube non valido/)).toBeInTheDocument();
      });
    });

    it('deve gestire configurazioni mancanti per GPX', () => {
      render(
        <UnifiedMediaUpload 
          {...defaultProps}
          config={{ enableGpx: true }}
          // onGpxUpload mancante intenzionalmente
        />
      );

      expect(screen.getByText('Carica File GPX')).toBeInTheDocument();
    });
  });
});