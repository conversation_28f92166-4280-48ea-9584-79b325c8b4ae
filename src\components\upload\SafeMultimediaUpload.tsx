// src/components/SafeMultimediaUpload.tsx
"use client";

import { useState } from 'react'
import { ErrorBoundary, MediaErrorFallback } from '@/components/ui/ErrorBoundary'
import UnifiedMediaUpload from '@/components/upload/UnifiedMediaUpload'
import { MediaItem, GpxFile } from '@/types/trip'
import { UseMediaUploadConfig } from '@/hooks/useMediaUpload'

interface SafeMultimediaUploadProps {
  mediaItems: MediaItem[];
  gpxFile?: GpxFile | null;
  onAddMedia: (media: Omit<MediaItem, 'id'>) => void;
  onRemoveMedia: (mediaId: string) => void;
  onUpdateCaption: (mediaId: string, caption: string) => void;
  onGpxUpload?: (gpx: GpxFile) => void;
  onGpxRemove?: () => void;
  config?: UseMediaUploadConfig;
  className?: string;
}

export default function SafeMultimediaUpload(props: SafeMultimediaUploadProps) {
  const [retryKey, setRetryKey] = useState(0)

  const handleRetry = () => {
    setRetryKey(prev => prev + 1)
  }

  return (
    <ErrorBoundary
      key={retryKey}
      fallback={<MediaErrorFallback onRetry={handleRetry} />}
    >
      <UnifiedMediaUpload {...props} />
    </ErrorBoundary>
  )
}